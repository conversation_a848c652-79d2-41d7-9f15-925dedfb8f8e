/**
 * @copyright: © 2025, 深圳市泛途智能科技有限公司. All rights reserved.
 * @file: sm_top.c
 * @description: 主状态处理, 子状态无关的处理.
 * @author: <PERSON>
 * @date: 2025-06-30
 */
#include <stdint.h>
#include <string.h>
#include "esp_timer.h" 
#include "sk_common.h"
#include "sk_log.h"
#include "sk_os.h"
#include "sk_board.h"
#include "sk_sm.h"
#include "sk_audio.h"
#include "sk_config.h"
#include "sk_wifi.h"
#include "sm.h"
#include "sk_opus.h"
#include "sk_opus_dec.h"
#include "sk_opus_enc.h"
#include "sk_clink.h"
#include "sk_rlink.h"
#include "sk_dfx.h"
#include "sk_ota_api.h"
#include "sk_http_downloader.h"

#define TAG "SmTop"

#define CONFIG_SK_TEST_PERIODIC_VOICE 0
#define LOCAL_CMD_MASK (1UL << SPEECH_CMD_EVENT_VOLDOWN | \
    1UL << SPEECH_CMD_EVENT_VOLUP | 1UL << SPEECH_CMD_EVENT_CONFIG | \
    1UL << SPEECH_CMD_EVENT_START_DBG | 1UL << SPEECH_CMD_EVENT_STOP_DBG | \
    1UL << SPEECH_CMD_EVENT_SLEEP | 1UL << SPEECH_CMD_EVENT_INFO | \
    1UL << SPEECH_CMD_EVENT_MIC_ON | 1UL << SPEECH_CMD_EVENT_MIC_OFF | \
    1UL << SPEECH_CMD_EVENT_MUSIC | 1UL << SPEECH_CMD_EVENT_PAUSE | \
    1UL << SPEECH_CMD_EVENT_RESUME | 1UL << SPEECH_CMD_EVENT_PREV | \
    1UL << SPEECH_CMD_EVENT_NEXT | 1UL << SPEECH_CMD_EVENT_QUIT)

typedef struct {
    int32_t state;
    uint8_t clinkState;             // 0: idle, 1: connecting, 2: connected
    uint8_t rlinkState;             // 0: idle, 1: connecting, 2: connected
    int32_t netState;               // 网络状态

    uint32_t secTime;               // 秒级计数
    int32_t runFlag;                // 状态机是否执行
    int32_t rebootCnt;              // reboot的秒级计数
    int16_t timeCnt;                // 网络连接使用的时间统计
    uint32_t cmdMask;               // 当前接受的按键掩码
    uint32_t waitWorkId;            // 等待本地提示语音的工作ID
    uint8_t otaFlag;                // 0: Unchecked, 1: Checked

    esp_timer_handle_t timer;       // 秒级定时器
    QueueHandle_t msgQueue;         // 消息队列
    SkSmItem smItemList[STATE_MAX]; // 子状态列表
    SkSpeechCmdProc *cmdProcMap;    // 按键处理映射表
    int32_t cmdProcMapLen;          // 按键处理映射表长度
    int16_t sessionId;              // 会话ID
    TaskHandle_t taskHandle;        // 状态机任务
    int32_t showCpuUsage;           // 显示CPU利用率的标志
    uint32_t taskStackBase[32];     // 任务栈的基地址
    uint16_t taskCpuCnt[32];        // 任务数量
} SkStateCtrl;

void SmConfigStartServer();
void SkSmEnterNewState(SkStateCtrl *ctrl, int32_t state, int32_t event, int32_t subEvent, int32_t param1);

SkStateCtrl g_topStateCtrl;

void SkSmShowSysState() {
    SkOsShowSysInfo();
    SkBspShowStat();
    SkOpusTaskShowStat();
    g_topStateCtrl.showCpuUsage = ~g_topStateCtrl.showCpuUsage;
}

void SkSmCmdDbgOn(int32_t event, int32_t subEvent, int32_t param1, int32_t param2) {
    SK_LOGI(TAG, "Debug on");
}

void SkSmVolNotice(int32_t vol) {
    uint8_t audioList[4];
    uint8_t audioLen;

    if (vol >= 100) {
        audioList[0] = AUDIO_IDX_VOICE_TO;
        audioList[1] = AUDIO_IDX_MAX;
        audioLen = 2;
    } else if (vol <= 30) {
        audioList[0] = AUDIO_IDX_VOICE_TO;
        audioList[1] = AUDIO_IDX_MIN;
        audioLen = 2;
    } else {
        audioList[0] = AUDIO_IDX_VOICE_TO;
        audioList[1] = AUDIO_IDX_0 + (vol / 10);
        audioList[2] = AUDIO_IDX_TEN;
        audioLen = 3;
    }
    SkSmPlayLocalNotice(audioList, audioLen, true);
}

int32_t SkSmSetVol(int32_t vol) {
    if (g_topStateCtrl.waitWorkId != 0) {
        SK_LOGE(TAG, "Set vol fail, prev oper %d not finished.",
            g_topStateCtrl.waitWorkId);
        return SK_RET_FAIL;
    }
    SkBspSetPlayVol(vol);
    SkSmVolNotice(vol);
    return SK_RET_SUCCESS;
}

int32_t SkSmVolUp() {
    int32_t vol = SkBspGetPlayVol();
    vol += 10;
    if (vol > 100) {
        vol = 100;
    }
    return SkSmSetVol(vol);
}

int32_t SkSmVolDown() {
    int32_t vol = SkBspGetPlayVol();
    vol -= 10;
    if (vol <= 30) {
        vol = 30;
    }
    return SkSmSetVol(vol);
}

void SkSmCmdVolUp(int32_t event, int32_t subEvent, int32_t param1, int32_t param2) {
    SkSmVolUp();
    return;
}

void SkSmCmdVolDown(int32_t event, int32_t subEvent, int32_t param1, int32_t param2) {
    SkSmVolDown();
    return;
}

void SkSmVolMax(int32_t event, int32_t subEvent, int32_t param1, int32_t param2) {
    SkSmSetVol(100);
    return;
}

void SkSmVolMin(int32_t event, int32_t subEvent, int32_t param1, int32_t param2) {
    SkSmSetVol(30);
    return;
}

void SkSmStartDbg(int32_t event, int32_t subEvent, int32_t param1, int32_t param2) {
    SkDfxLinkStart();
    return;
}

void SkSmStopDbg(int32_t event, int32_t subEvent, int32_t param1, int32_t param2) {
    SkDfxLinkStop();
    return;
}

void SkSmStartRec(int32_t event, int32_t subEvent, int32_t param1, int32_t param2) {
#if (CONFIG_PCM_DEBUG != CONFIG_PCM_DBG_CLOSE)
    SkRecorderResume();
    SkVcProcessEnable(true);    
#endif
}

void SkSmStopRec(int32_t event, int32_t subEvent, int32_t param1, int32_t param2) {
#if (CONFIG_PCM_DEBUG != CONFIG_PCM_DBG_CLOSE)
    SkVcProcessEnable(false);
#endif
}

void SkSmStartPm(int32_t event, int32_t subEvent, int32_t param1, int32_t param2) {
    SkSmEnterNewState(&g_topStateCtrl, STATE_PM, 0, 0, 0);
    return;
}

void SkSmActionDone() {
    uint8_t audioList[2];

    audioList[0] = AUDIO_IDX_DONE;
    audioList[1] = 0;
    SkSmPlayLocalNotice(audioList, 1, true);
    return;
}

void SkSmActionOk() {
    uint8_t audioList[2];

    audioList[0] = AUDIO_IDX_OK;
    audioList[1] = 0;
    SkSmPlayLocalNotice(audioList, 1, true);
    return;
}

void SkSmCmdSysInfo(int32_t event, int32_t subEvent, int32_t param1, int32_t param2) {
    SkSmShowSysState();
    SkOpusDecStat();
    SkOpusEncShowStatus();
    return;
}

SkSpeechCmdProc g_cmdProcMap[] = {
    NULL,
    SkSmStateChange,        // SPEECH_CMD_EVENT_CHAT
    SkSmStateChange,        // SPEECH_CMD_EVENT_CALL
    SkSmStateChange,        // SPEECH_CMD_EVENT_MUSIC
    SkSmStateChange,        // SPEECH_CMD_EVENT_CONFIG
    SkSmStateChange,        // SPEECH_CMD_EVENT_QUERY
    SkSmCmdVolUp,           // SPEECH_CMD_EVENT_VOLUP
    SkSmCmdVolDown,         // SPEECH_CMD_EVENT_VOLDOWN
    SkSmCmdDbgOn,           // SPEECH_CMD_EVENT_HELP
    NULL,                   // SPEECH_CMD_EVENT_PAUSE
    NULL,                   // SPEECH_CMD_EVENT_CONFIRM
    NULL,                   // SPEECH_CMD_EVENT_QUIT
    NULL,                   // SPEECH_CMD_EVENT_PREV
    NULL,                   // SPEECH_CMD_EVENT_NEXT
    NULL,                   // SPEECH_CMD_EVENT_RESUME,
    SkSmCmdSysInfo,         // SPEECH_CMD_EVENT_INFO
    SkSmVolMax,             // SPEECH_CMD_EVENT_VOLMAX
    SkSmVolMin,             // SPEECH_CMD_EVENT_VOLMIN
    SkSmStartDbg,           // SPEECH_CMD_EVENT_START_DBG
    SkSmStopDbg,            // SPEECH_CMD_EVENT_STOP_DBG
    SkSmStartPm,            // SPEECH_CMD_EVENT_SLEEP
    SkSmStateChange,        // SPEECH_CMD_EVENT_CALL_CALLEE
    SkSmStartRec,           // SPEECH_CMD_EVENT_MIC_ON
    SkSmStopRec,            // SPEECH_CMD_EVENT_MIC_OFF
};

int32_t SmCmdToState(int32_t cmd) {
    int32_t state = STATE_IDLE;

    switch (cmd) {
        case SPEECH_CMD_EVENT_CHAT:
            state = STATE_CALL;
            break;
        case SPEECH_CMD_EVENT_CALL:
        case SPEECH_CMD_EVENT_CALL_CALLEE:
            state = STATE_CALL;
            break;
        case SPEECH_CMD_EVENT_MUSIC:
            state = STATE_MUSIC;
            break;
        case SPEECH_CMD_EVENT_CONFIG:
            state = STATE_CONFIG;
            break;
        case SPEECH_CMD_EVENT_QUERY:
            state = STATE_QUERY;
            break;
        case SPEECH_CMD_EVENT_HELP:
            state = STATE_HELP;
            break;
        default:
            state = STATE_IDLE;
    }
    return state;
}

void SkSmEnterNewState(SkStateCtrl *ctrl, int32_t state, int32_t event, int32_t subEvent, int32_t param1) {
    SkSmEvent smEvent;
    SkSmItem *smItem = NULL;

    ctrl->state = state;
    SK_LOGI(TAG, "State start %d", ctrl->state);
    smItem = &ctrl->smItemList[ctrl->state];
    if (smItem->startProc != NULL) {
        smEvent.event = event;
        smEvent.subEvent = subEvent;
        smEvent.param1 = param1;
        smEvent.param2 = 0;
        smItem->startProc(&smItem->info, &smEvent);
    }

    return;
}

void SkSmStopPrev(SkStateCtrl *ctrl) {
    SkSmItem *smItem = NULL;

    if (ctrl->state >= STATE_MAX || ctrl->state < STATE_INIT) {
        return;
    }

    if (ctrl->state != STATE_IDLE) {
        SK_LOGI(TAG, "State stop %d", ctrl->state);
        smItem = &ctrl->smItemList[ctrl->state];
        if (smItem->stopProc != NULL) {
            smItem->stopProc(&smItem->info);
        }
        ctrl->state = STATE_IDLE;
        SkRledSetEvent(SK_LED_EVENT_IDLE);
    }
    return;
}

void SkSmStateChange(int32_t event, int32_t subEvent, int32_t param1, int32_t param2) {
    SkStateCtrl *ctrl = &g_topStateCtrl;
    int32_t state = SmCmdToState(subEvent);

    SkSmStopPrev(ctrl);
    SkSmEnterNewState(ctrl, state, event, subEvent, param1);

    return;
}

bool SkSmTopCmdProc(SkStateCtrl *ctrl, int32_t cmd) {
    if (ctrl->waitWorkId != 0) {
        SK_LOGE(TAG, "Wait work %d", ctrl->waitWorkId);
        return false;
    }
    if (cmd >= ctrl->cmdProcMapLen) {
        SK_LOGE(TAG, "Invalid command %d", cmd);
        return false;
    }
    if ((ctrl->cmdMask & (1UL << cmd)) == 0UL) {
        SK_LOGE(TAG, "Command %d not allowed", cmd);
        return true;
    }

    SkSpeechCmdProc cmdProc = ctrl->cmdProcMap[cmd];
    if (cmdProc != NULL) {
        SK_LOGE(TAG, "Command %d", cmd);
        cmdProc(SM_EVENT_CMD, cmd, 0, 0);
        return false;
    }
    return true;
}

int32_t SkSmGenAudioList(int32_t wifiMode, uint8_t firstIdx, uint8_t audioList[16]) {
    uint8_t ipStr[16];
    int32_t ipStrLen;

    SkWifiGetIp(wifiMode, (char *)ipStr, sizeof(ipStr));
    audioList[0] = firstIdx;
    ipStrLen = strlen((char *)ipStr);
    for (int i = 0; i < ipStrLen; i++) {
        if (ipStr[i] == '.') {
            audioList[1 + i] = AUDIO_IDX_POINT;
        } else {
            audioList[1 + i] = ipStr[i] - '0' + AUDIO_IDX_0;
        }
    }

    return 1 + ipStrLen;
}


void SmOnClinkConnectedEvent(SkStateCtrl *ctrl) {
    SkSmSendEvent((SkStateHandler)ctrl, SM_EVENT_SYSTEM, SM_EVENT_SYSTEM_ENTER_MENU, 0, 0);

    return;
}

void SmOnNetworkConnectedEvent() {
    uint8_t audioList[16];
    uint32_t listLen;

    listLen = SkSmGenAudioList(SK_WIFI_MODE_STA, AUDIO_IDX_CONNECTED, audioList);
    SkSmPlayLocalNotice(audioList, listLen, true);

    return;
}

bool SmOnNetworkEvent(SkStateCtrl *ctrl, int32_t subEvent) {
    if (subEvent == SM_EVENT_NETWORK_CONNECTING) {
        ctrl->netState = NETWORK_STATE_STA_CONNECTING;
        SK_LOGI(TAG, "Network connecting");
        SkClinkSetFunFlag(CLINK_RUN_FLAG_IDLE);
    } else if (subEvent == SM_EVENT_NETWORK_CONNECTED) {
        ctrl->netState = NETWORK_STATE_STA_CONNECTED;
        SK_LOGI(TAG, "Network connected");
        SmOnNetworkConnectedEvent();
    } else if (subEvent == SM_EVENT_NETWORK_STOP) {
        ctrl->netState = NETWORK_STATE_STA_STOP;
        SK_LOGI(TAG, "Network sta stopped");
        SkClinkSetFunFlag(CLINK_RUN_FLAG_IDLE);
    } else if (subEvent == SM_EVENT_NETWORK_SCANNING) {
        ctrl->netState = NETWORK_STATE_STA_CONNECTING;
        SK_LOGI(TAG, "Network scanning");
    } else if (subEvent == SM_EVENT_NETWORK_AP_ENABLE) {
        ctrl->netState = NETWORK_STATE_AP_ENABLE;
        SmConfigStartServer();
        SK_LOGI(TAG, "Network AP enable");
    } else if (subEvent == SM_EVENT_NETWORK_AP_STOP) {
        SK_LOGI(TAG, "Network AP stop");
        ctrl->netState = NETWORK_STATE_AP_STOP;
    }

    return false;
}

void SkSmMovedEventProc(SkStateCtrl *ctrl) {
    uint8_t audioList[2];
    if ((ctrl->state != STATE_IDLE) || (ctrl->waitWorkId == 0)) {
        return;
    }
    
    audioList[0] = AUDIO_IDX_SIU;
    SkSmPlayLocalNotice(audioList, 1, true);

    return;
}

void SkSmRledBlink(SkStateCtrl *ctrl) {
    const int8_t ledEventMap[] = {
        SK_LED_EVENT_INIT,
        SK_LED_EVENT_CONNECTING,
        SK_LED_EVENT_OTA,
        SK_LED_EVENT_IDLE,
        SK_LED_EVENT_ERROR,
        SK_LED_EVENT_CHAT,
        SK_LED_EVENT_CALLING,
        SK_LED_EVENT_MUSIC,
        SK_LED_EVENT_CONFIG,
        SK_LED_EVENT_STORY,
        SK_LED_EVENT_STORY,
        SK_LED_EVENT_STORY,
        SK_LED_EVENT_DOWN,
    };
    int32_t ledEvent = SK_LED_EVENT_IDLE;

    if (ctrl->state >= ARRAY_SIZE(ledEventMap)) {
        return;
    }
    // 后续如果有细分状态，再在这里添加处理，当前只处理几种状态
    ledEvent = ledEventMap[ctrl->state];
    // 实现闪烁效果，每隔一秒切换一次明暗
    if ((ctrl->secTime & 0x1) == 0) {
        ledEvent = SK_LED_EVENT_DOWN;
    }
    SkRledSetEvent(ledEvent);
    return;
}

bool SkSmSystemEventProc(SkStateCtrl *ctrl, SkSmEvent *event) {
    bool continueProc = false;
    uint8_t audioList[2];

    if (event->subEvent == SM_EVENT_SYSTEM_INIT_OK) {
        audioList[0] = AUDIO_IDX_START;
        SkSmPlayLocalNotice(audioList, 1, false);
        SK_LOGE(TAG, "Start Connect Network");
        SkWifiStartSta();
        ctrl->state = STATE_CONNECTING;
        ctrl->timeCnt = 0;
        SkRecorderResume();
    } else if (event->subEvent == SM_EVENT_SYSTEM_TICK) {
        ctrl->secTime++;
        SkSmRledBlink(ctrl);
        int32_t key = SkBspGetFuncKey();
        if (key == 1) {
            SkDfxLinkStart();
        } else if (key == 2) {
            SkDfxLinkStop();
        }
#if CONFIG_SK_TEST_PERIODIC_VOICE
        audioList[0] = AUDIO_IDX_0 + (ctrl->secTime % 10);
        SkSmPlayLocalNotice(audioList, 1, false);
#endif
        if (ctrl->state == STATE_REBOOT) {
            ctrl->rebootCnt++;
            if (ctrl->rebootCnt >= 10) {
                SkOsReboot();
            }
            return false;
        }
#ifdef CONFIG_SK_CPU_PERIODIC_SHOW
        if (ctrl->showCpuUsage != 0) {
            SkSmShowCpuUsage(ctrl->taskStackBase, ctrl->taskCpuCnt, ARRAY_SIZE(ctrl->taskCpuCnt));
        }
#endif
        continueProc = true;
    } else if (event->subEvent == SM_EVENT_SYSTEM_REBOOT) {
        SK_LOGI(TAG, "System reboot request");
        SkSmStopPrev(ctrl);
        SkSmSetReboot();
        continueProc = false;
    } else if (event->subEvent == SM_EVENT_SYSTEM_OTA_COMPLETE) {
        SK_LOGI(TAG, "OTA upgrade completed, rebooting now...");
        SkSmStopPrev(ctrl);
        SkSmSetReboot();
        continueProc = false;
    } else if (event->subEvent == SM_EVENT_SYSTEM_OTA_FAILED) {
        SK_LOGE(TAG, "OTA upgrade failed");
        // 处理OTA失败的情况，可以播放失败提示音
        continueProc = false;
    } else if (event->subEvent == SM_EVENT_SYSTEM_USER_ACK) {
        audioList[0] = AUDIO_IDX_HERE;
        SkSmPlayLocalNotice(audioList, 1, false);
        SK_LOGE(TAG, "Action user ack");
        continueProc = false;
    } else if (event->subEvent == SM_EVENT_SYSTEM_WORK_FINISH) {
        SK_LOGD(TAG, "Work finish %u, wait work %u", event->param1, ctrl->waitWorkId);
        if (ctrl->waitWorkId <= event->param1) {
            ctrl->waitWorkId = 0;
            continueProc = true;
        }
    } else if (event->subEvent == SM_EVENT_SYSTEM_MOVED) {
        SkSmMovedEventProc(ctrl);
    } else if (event->subEvent == SM_EVENT_SYSTEM_WAKEUP) {
        if (ctrl->clinkState == 2) {
            if (ctrl->state == STATE_IDLE) {
                SkSmEnterNewState(ctrl, STATE_CALL, event->event, event->subEvent, 0);
            }
        }
        continueProc = true;
    } else if (event->subEvent == SM_EVENT_SYSTEM_ENTER_MENU) {
        SkSmEnterNewState(ctrl, STATE_CALL, event->event, event->subEvent, 0);
        continueProc = false;
    }

    return continueProc;
}

bool SkSmLinkEventProc(SkStateCtrl *ctrl, SkSmEvent *event) {
    bool continueProc = true;
    SkSmItem *smItem = NULL;

    if (event->subEvent == SM_EVENT_CLINK_CALL_REQUEST) {
        if (ctrl->state != STATE_IDLE) {
            SK_LOGI(TAG, "State is %d, reject call", ctrl->state);
        } else {
            SK_LOGI(TAG, "State start %d", STATE_CALL);
            ctrl->state = STATE_CALL;
            smItem = &ctrl->smItemList[ctrl->state];
            smItem->startProc(&smItem->info, event);   
        }
        continueProc = true;
    } else if (event->subEvent == SM_EVENT_CLINK_CONNECT) {
        SmOnClinkConnectedEvent(ctrl);
        ctrl->clinkState = SM_CLINK_STATE_CONNECTED;
        ctrl->cmdMask = 0xFFFFFFFF;
        continueProc = true;
    } else if (event->subEvent == SM_EVENT_CLINK_DISCONNECT) {
        ctrl->cmdMask = LOCAL_CMD_MASK;
        // 主控丢失消息, 在每个状态里需要用于异常退出，设置为继续处理
        ctrl->clinkState = SM_CLINK_STATE_DISCONNECT;
        continueProc = false;
    } else if (event->subEvent == SM_EVENT_CLINK_CONNECTING) {
        ctrl->clinkState = SM_CLINK_STATE_CONNECTING;
        continueProc = false;
    } else if (event->subEvent == SM_EVENT_RLINK_DISCONNECT) {
        ctrl->rlinkState = SM_RLINK_STATE_DISCONNECT;
        continueProc = true;
    } else if (event->subEvent == SM_EVENT_RLINK_CONNECT) {
        ctrl->rlinkState = SM_RLINK_STATE_CONNECTED;
        continueProc = true;
    }

    return continueProc;
}

int32_t SkSmConnectingStateProc(SkSubStateInfo *info, const SkSmEvent *event) {
    SkStateCtrl *ctrl = (SkStateCtrl *)info->privateData;
    uint8_t audioList[2];

    if (event->event == SM_EVENT_SYSTEM && event->subEvent == SM_EVENT_SYSTEM_TICK) {
        if (SkSmGetNetState() == NETWORK_STATE_STA_CONNECTED) {
            if (ctrl->otaFlag == 0) {
                SkSmEnterNewState(ctrl, STATE_OTA, 0, 0, 0);
                SK_LOGI(TAG, "Network connected, start OTA");    
            } else {
                SkClinkSetFunFlag(CLINK_RUN_FLAG_START);
                SkSmEnterNewState(ctrl, STATE_IDLE, 0, 0, 0);
                SK_LOGI(TAG, "Network connected, start idle");
            }
            return SK_RET_SUCCESS;
        }
        
        ctrl->timeCnt++;
        if (ctrl->timeCnt == 60) {
            SK_LOGE(TAG, "Connect Network Timeout");
            audioList[0] = AUDIO_IDX_NEED_CFG;
            SkSmPlayLocalNotice(audioList, 1, true); 
        }
    } else if (event->event == SM_EVENT_CMD) {
        SK_LOGE(TAG, "command at connecting state %d", ctrl->timeCnt);
        if (ctrl->timeCnt < 60) {
            audioList[0] = AUDIO_IDX_CONNECTING;
        } else {
            audioList[0] = AUDIO_IDX_NEED_CFG;
        }
        SkSmPlayLocalNotice(audioList, 1, true);
    }

    return SK_RET_SUCCESS;
}

void SkSmMain(void *arg) {
    bool continueProc;
    int32_t ret;
    SkStateCtrl *ctrl = (SkStateCtrl *)arg;
    SkSmItem *smItem = NULL;
    SkSmEvent event;
    
    while (ctrl->runFlag != 0) {
        ret = xQueueReceive(ctrl->msgQueue, &event, portMAX_DELAY);
        if (ret != pdPASS) {
            SK_LOGE(TAG, "Failed to receive message");
            continue;
        }

        if (event.event != SM_EVENT_SYSTEM || event.subEvent != SM_EVENT_SYSTEM_TICK) {
            SK_LOGD(TAG, "State %d Event %d SubEvent %d param1 %d param2 %d", 
                ctrl->state, event.event, event.subEvent, event.param1, event.param2);
        }
        if (ctrl->state > STATE_MAX) {
            SK_LOGE(TAG, "Invalid state %d", ctrl->state);
            return;
        }
        
        if (event.event == SM_EVENT_CMD) {
            continueProc = SkSmTopCmdProc(ctrl, event.subEvent);
        } else if (event.event == SM_EVENT_LINK) {
            continueProc = SkSmLinkEventProc(ctrl, &event);
        } else if (event.event == SM_EVENT_NETWORK) {
            continueProc = SmOnNetworkEvent(ctrl, event.subEvent);
        } else if (event.event == SM_EVENT_SYSTEM) {
            continueProc = SkSmSystemEventProc(ctrl, &event);
        } else if (event.event == SM_EVENT_HTTP_AUDIO) {
            continueProc = true;  // HTTP音频事件总是传递给当前状态处理
        } else {
            continueProc = false;
        }
        
        if (continueProc) {
            smItem = &ctrl->smItemList[ctrl->state];
            if (smItem->eventProc != NULL) {
                smItem->eventProc(&smItem->info, &event);
            }
        }
    }
}

void SmTimerCallback(void* arg) {
    SkSmSendEvent((SkStateHandler)arg, SM_EVENT_SYSTEM, SM_EVENT_SYSTEM_TICK, 0, 0);
    return;
}

void SmStateEndCallback(SkStateHandler handler, int32_t state) {
    SkStateCtrl *ctrl = (SkStateCtrl*)handler;

    if (ctrl->state == state) {
        SK_LOGI(TAG, "State end %d", ctrl->state);
        if (ctrl->state == STATE_OTA) {
            ctrl->otaFlag = 1;
            SkClinkSetFunFlag(CLINK_RUN_FLAG_START);
        } else if (ctrl->state == STATE_PM) {
            ctrl->timeCnt = 0;
            ctrl->state = STATE_CONNECTING;
            return;    
        }
        
        SkSmEnterNewState(ctrl, STATE_IDLE, 0, 0, 0);
    }

    return;
}

SkStateHandler SkSmInit() {
    SkStateCtrl *ctrl = &g_topStateCtrl;
    const esp_timer_create_args_t timerArgs = {
        .callback = &SmTimerCallback,
        .name = "SmSecTimer",
        .arg = ctrl,
    };

    ctrl->state = STATE_IDLE;
    ctrl->msgQueue = xQueueCreate(32, sizeof(SkSmEvent));
    ctrl->cmdProcMap = g_cmdProcMap;
    ctrl->cmdProcMapLen = sizeof(g_cmdProcMap) / sizeof(SkSpeechCmdProc);
    ctrl->runFlag = 1;
    ctrl->netState = NETWORK_STATE_STA_STOP;
    ctrl->showCpuUsage = 0;
    ctrl->cmdMask = LOCAL_CMD_MASK;
    SkSmIdleInit(&ctrl->smItemList[STATE_IDLE], NULL, (SkStateHandler)ctrl);
    SkSmCallInit(&ctrl->smItemList[STATE_CALL], SmStateEndCallback, (SkStateHandler)ctrl);
    SkSmMusicInit(&ctrl->smItemList[STATE_MUSIC], SmStateEndCallback, (SkStateHandler)ctrl);
    SkSmConfigInit(&ctrl->smItemList[STATE_CONFIG], NULL, (SkStateHandler)ctrl);
    SkSmQueryInit(&ctrl->smItemList[STATE_QUERY], NULL, (SkStateHandler)ctrl);
    SkSmHelpInit(&ctrl->smItemList[STATE_HELP], NULL, (SkStateHandler)ctrl);
    SkSmConnectingInit(&ctrl->smItemList[STATE_CONNECTING], NULL, (SkStateHandler)ctrl);
    SkSmOtaInit(&ctrl->smItemList[STATE_OTA], SmStateEndCallback, (SkStateHandler)ctrl);
    SkSmPmInit(&ctrl->smItemList[STATE_PM], SmStateEndCallback, (SkStateHandler)ctrl);
    SkSmHttpAudioInit(&ctrl->smItemList[STATE_HTTP_AUDIO], NULL, (SkStateHandler)ctrl);

    ESP_ERROR_CHECK(esp_timer_create(&timerArgs, &ctrl->timer));
    SK_LOGI(TAG, "State machine init msgQueue is %p", ctrl->msgQueue);
    // 设置定时器为周期性触发，每1秒触发一次
    ESP_ERROR_CHECK(esp_timer_start_periodic(ctrl->timer, 1000 * 1000)); // 时间单位为微秒
    xTaskCreate(SkSmMain, "SkStateMachine", 8192, (void*)&g_topStateCtrl, 5, &g_topStateCtrl.taskHandle);
    SK_LOGI(TAG, "stack base %p", pxTaskGetStackStart(g_topStateCtrl.taskHandle));
    return (SkStateHandler)ctrl;
}

void SkSmSendEvent(SkStateHandler handler, int32_t event, int32_t subEvent, uint32_t param1, uint32_t param2) {
    SkStateCtrl *ctrl = (SkStateCtrl*)handler;
    SkSmEvent smEvent;

    smEvent.event = event;
    smEvent.subEvent = subEvent;
    smEvent.param1 = param1;
    smEvent.param2 = param2;
    xQueueSend(ctrl->msgQueue, &smEvent, portMAX_DELAY);

    return;
}

void SkSmPostEvent(int32_t event, int32_t subEvent, uint32_t param1, uint32_t param2) {
    SkStateCtrl *ctrl = &g_topStateCtrl;
    SkSmEvent smEvent;

    smEvent.event = event;
    smEvent.subEvent = subEvent;
    smEvent.param1 = param1;
    smEvent.param2 = param2;
    xQueueSend(ctrl->msgQueue, &smEvent, portMAX_DELAY);
}

uint8_t SkSmGetClinkState(SkStateHandler handler) {
    SkStateCtrl *ctrl = (SkStateCtrl *)handler;
    return ctrl->clinkState;
}

uint8_t SkSmGetRlinkState(SkStateHandler handler) {
    SkStateCtrl *ctrl = (SkStateCtrl *)handler;
    return ctrl->rlinkState;
}

typedef struct {
    uint32_t idleTime;
    SkStateHandler handler;
} SmIdleCtrl;

SmIdleCtrl g_idleCtrl;

int32_t SkSmIdleStart(SkSubStateInfo *info, const SkSmEvent *event) {
    SmIdleCtrl *ctrl = (SmIdleCtrl*)info->privateData;
    ctrl->idleTime = 0;
    SK_LOGI(TAG, "Idle state start.");
    SkRecorderResume();
    SkSrProcessEnable(true);
    SkVcProcessEnable(false);
    SkRledSetEvent(SK_LED_EVENT_IDLE);
    return SK_RET_SUCCESS;
}

int32_t SkSmIdleStop(SkSubStateInfo *info) {
    return SK_RET_SUCCESS;
}

int32_t SkSmIdleEventProc(SkSubStateInfo *info, const SkSmEvent *event) {
    SmIdleCtrl *ctrl = (SmIdleCtrl*)info->privateData;
    if (event->event == SM_EVENT_SYSTEM && event->subEvent == SM_EVENT_SYSTEM_TICK) {
        ctrl->idleTime++;

        // 检查是否需要触发OTA更新（简单的一行调用）
        SkOtaManagerOnIdleTick(ctrl->idleTime);

        if (ctrl->idleTime == 20) {
            SK_LOGI(TAG, "Idle time %d, %d", ctrl->idleTime, SkSensorState());
            //if (SkSensorState() >= 600) {
            //SkBoardModemSetPm(true);
            //}
        }
    }
    return SK_RET_SUCCESS;
}

int16_t SkSmNewSession(SkStateHandler handler) {
    SkStateCtrl *ctrl = (SkStateCtrl*)handler;
    ctrl->sessionId++;
    if (ctrl->sessionId == 0) {
        ctrl->sessionId++;
    }
    return ctrl->sessionId;
}

int32_t SkSmIdleInit(SkSmItem *item, SkSmStateEndCallback endCb, SkStateHandler handler) {
    item->info.subState = 0;
    item->info.privateData = &g_idleCtrl;
    item->startProc = SkSmIdleStart;
    item->stopProc = SkSmIdleStop;
    item->eventProc = SkSmIdleEventProc;
    g_idleCtrl.handler = handler;

    return SK_RET_SUCCESS;
}

void SkSmOnWifiEvent(uint32_t event) {
    uint16_t subEvent;

    switch (event) {
        case SK_WIFI_EVENT_AP_STOP:
            subEvent = SM_EVENT_NETWORK_AP_STOP;
            break;
        case SK_WIFI_EVENT_AP_START:
            subEvent = SM_EVENT_NETWORK_AP_ENABLE;
            break;
        case SK_WIFI_EVENT_STA_STOP:
            subEvent = SM_EVENT_NETWORK_STOP;
            break;
        case SK_WIFI_EVENT_STA_SCAN:
            subEvent = SM_EVENT_NETWORK_SCANNING;
            break;
        case SK_WIFI_EVENT_STA_CONNECTING:
            subEvent = SM_EVENT_NETWORK_CONNECTING;
            break;
        case SK_WIFI_EVENT_STA_CONNECTED:
            subEvent = SM_EVENT_NETWORK_CONNECTED;
            break;
        default:
            return;
    }
    SkSmSendEvent(&g_topStateCtrl, SM_EVENT_NETWORK, subEvent, 0, 0);
    return;
}

void SkSmOnUserAck() {
    SkSmSendEvent(&g_topStateCtrl, SM_EVENT_SYSTEM, SM_EVENT_SYSTEM_USER_ACK, 0, 0);
    return;
}

void SkSmOnWorkFinish(uint32_t workId) {
    SkSmSendEvent(&g_topStateCtrl, SM_EVENT_SYSTEM, SM_EVENT_SYSTEM_WORK_FINISH, workId, 0);
    return;
}

void SkSmOnSessionDecFinish(uint16_t sessionId) {
    SkSmSendEvent(&g_topStateCtrl, SM_EVENT_LINK, SM_EVENT_RLINK_SESSION_AUDIO_END, sessionId, 0);
    return;
}

void SkSmOnWakeup() {
    SkSmSendEvent(&g_topStateCtrl, SM_EVENT_SYSTEM, SM_EVENT_SYSTEM_WAKEUP, 0, 0);
    return;
}

void SkSmOnReboot() {
    SkSmSendEvent(&g_topStateCtrl, SM_EVENT_SYSTEM, SM_EVENT_SYSTEM_REBOOT, 0, 0);
}

int32_t SkSmQueryInit(SkSmItem *item, SkSmStateEndCallback endCb, SkStateHandler handler) {
    item->info.subState = 0;
    item->info.privateData = NULL;
    item->startProc = NULL;
    item->stopProc = NULL;
    item->eventProc = NULL;
    return SK_RET_SUCCESS;
}

int32_t SkSmHelpInit(SkSmItem *item, SkSmStateEndCallback endCb, SkStateHandler handler) {
    item->info.subState = 0;
    item->info.privateData = NULL;
    item->startProc = NULL;
    item->stopProc = NULL;
    item->eventProc = NULL;
    return SK_RET_SUCCESS;
}

int32_t SkSmConnectingInit(SkSmItem *item, SkSmStateEndCallback endCb, SkStateHandler handler) {
    item->info.subState = 0;
    item->info.privateData = handler;
    item->startProc = NULL;
    item->stopProc = NULL;
    item->eventProc = SkSmConnectingStateProc;
    return SK_RET_SUCCESS;
}

int32_t SkSmGetNetState(void) {
    return g_topStateCtrl.netState;
}

// HTTP下载任务
static void HttpDownloadTask(void *param) {
    SkHttpAudioContext *ctx = (SkHttpAudioContext*)param;

    SK_LOGI(TAG, "HTTP download task started for: %s", ctx->url);
    ctx->currentState = HTTP_AUDIO_STATE_DOWNLOADING;

    // 分配下载数据结构
    ctx->downloadData = malloc(sizeof(SkHttpDownloadData));
    if (ctx->downloadData == NULL) {
        SK_LOGE(TAG, "Failed to allocate download data");
        ctx->lastError = SK_RET_NO_MEMORY;
        SkSmSendEvent(&g_topStateCtrl, SM_EVENT_HTTP_AUDIO, SM_EVENT_HTTP_AUDIO_ERROR, 0, 0);
        ctx->currentTask = NULL;
        vTaskDelete(NULL);
        return;
    }

    // 执行HTTP下载
    sk_err_t ret = SkHttpDownloadFile(ctx->url, ctx->downloadData);

    if (ret == SK_RET_SUCCESS) {
        SK_LOGI(TAG, "HTTP download completed: %zu bytes", ctx->downloadData->totalSize);
        // 通知状态机：下载完成，进入OGG解析状态
        SkSmSendEvent(&g_topStateCtrl, SM_EVENT_HTTP_AUDIO, SM_EVENT_HTTP_DOWNLOAD_COMPLETE, 0, 0);
    } else {
        SK_LOGE(TAG, "HTTP download failed: %d", ret);
        ctx->lastError = ret;
        SkSmSendEvent(&g_topStateCtrl, SM_EVENT_HTTP_AUDIO, SM_EVENT_HTTP_AUDIO_ERROR, 0, 0);
    }

    ctx->currentTask = NULL;
    vTaskDelete(NULL);
}

// OGG解析任务
static void OggParseTask(void *param) {
    SkHttpAudioContext *ctx = (SkHttpAudioContext*)param;

    SK_LOGI(TAG, "OGG parse task started");
    ctx->currentState = HTTP_AUDIO_STATE_PARSING;

    if (ctx->downloadData == NULL) {
        SK_LOGE(TAG, "Invalid download data");
        ctx->lastError = SK_RET_INVALID_PARAM;
        SkSmSendEvent(&g_topStateCtrl, SM_EVENT_HTTP_AUDIO, SM_EVENT_HTTP_AUDIO_ERROR, 0, 0);
        ctx->currentTask = NULL;
        vTaskDelete(NULL);
        return;
    }

    // 解析OPUS头部信息
    sk_err_t ret = SkOggParseOpusHeader(ctx->downloadData->ringbuf, &ctx->opusHeader);

    if (ret == SK_RET_SUCCESS) {
        SK_LOGI(TAG, "OGG parsing completed: %dch, %luHz",
               ctx->opusHeader.channels, ctx->opusHeader.sampleRate);
        // 通知状态机：OGG解析完成，进入OPUS队列状态
        SkSmSendEvent(&g_topStateCtrl, SM_EVENT_HTTP_AUDIO, SM_EVENT_OGG_PARSE_COMPLETE, 0, 0);
    } else {
        SK_LOGE(TAG, "OGG parsing failed: %d", ret);
        ctx->lastError = ret;
        SkSmSendEvent(&g_topStateCtrl, SM_EVENT_HTTP_AUDIO, SM_EVENT_HTTP_AUDIO_ERROR, 0, 0);
    }

    ctx->currentTask = NULL;
    vTaskDelete(NULL);
}

// OPUS队列播放任务
static void OpusQueueTask(void *param) {
    SkHttpAudioContext *ctx = (SkHttpAudioContext*)param;

    SK_LOGI(TAG, "OPUS queue task started");
    ctx->currentState = HTTP_AUDIO_STATE_PLAYING;

    if (ctx->downloadData == NULL) {
        SK_LOGE(TAG, "Invalid download data");
        ctx->lastError = SK_RET_INVALID_PARAM;
        SkSmSendEvent(&g_topStateCtrl, SM_EVENT_HTTP_AUDIO, SM_EVENT_HTTP_AUDIO_ERROR, 0, 0);
        ctx->currentTask = NULL;
        vTaskDelete(NULL);
        return;
    }

    // 获取OPUS解码器句柄
    SkOpusDecHandler handler = SkOpusDecGetHandler();
    if (handler == NULL) {
        SK_LOGE(TAG, "OPUS decoder not available");
        ctx->lastError = SK_RET_FAIL;
        SkSmSendEvent(&g_topStateCtrl, SM_EVENT_HTTP_AUDIO, SM_EVENT_HTTP_AUDIO_ERROR, 0, 0);
        ctx->currentTask = NULL;
        vTaskDelete(NULL);
        return;
    }

    // 重置OGG解析状态
    SkOggResetParserPosition();

    ctx->processedPackets = 0;
    bool spkStarted = false;

    // OPUS包处理循环
    while (true) {
        SkOpusPacket packet;

        // 获取下一个OPUS包
        sk_err_t ret = SkOggGetNextOpusPacket(ctx->downloadData->ringbuf, &packet);
        if (ret != SK_RET_SUCCESS) {
            if (ret == SK_RET_NOT_FOUND) {
                SK_LOGI(TAG, "All OPUS packets processed: %lu", ctx->processedPackets);
                break;
            } else {
                SK_LOGE(TAG, "Failed to get OPUS packet: %d", ret);
                ctx->lastError = ret;
                SkSmSendEvent(&g_topStateCtrl, SM_EVENT_HTTP_AUDIO, SM_EVENT_HTTP_AUDIO_ERROR, 0, 0);
                ctx->currentTask = NULL;
                vTaskDelete(NULL);
                return;
            }
        }

        ctx->processedPackets++;

        // 使用静态PCM缓冲区（在任务栈中，不在主任务栈）
        static int16_t pcmBuffer[960];

        // 解码OPUS包
        int32_t samples = SkOpusDecDecode(handler, packet.data, packet.size, pcmBuffer, 960);

        if (samples > 0) {
            // 启动扬声器（如果还没启动）
            if (!spkStarted) {
                SkBspStartSpk();
                spkStarted = true;
                SK_LOGI(TAG, "Speaker started for audio playback");
            }

            // 播放PCM数据
            size_t pcmBytes = samples * sizeof(int16_t);
            sk_err_t playRet = SkBspPlayAudio(pcmBuffer, pcmBytes, 100);

            if (playRet != SK_RET_SUCCESS) {
                SK_LOGE(TAG, "Failed to play audio: %d", playRet);
            }
        }

        // 释放OPUS包
        SkOggFreeOpusPacket(&packet);

        // 定期让出CPU时间
        if (ctx->processedPackets % 10 == 0) {
            vTaskDelay(pdMS_TO_TICKS(1));
        }
    }

    SK_LOGI(TAG, "OPUS queue processing completed: %lu packets", ctx->processedPackets);

    // 通知状态机：OPUS队列处理完成
    SkSmSendEvent(&g_topStateCtrl, SM_EVENT_HTTP_AUDIO, SM_EVENT_OPUS_QUEUE_COMPLETE, 0, 0);

    ctx->currentTask = NULL;
    vTaskDelete(NULL);
}

// HTTP音频状态开始处理
int32_t SmHttpAudioStart(SkSubStateInfo *info, const SkSmEvent *event) {
    SK_LOGI(TAG, "HTTP audio state started");

    // 清理之前的上下文
    memset(&g_httpAudioCtx, 0, sizeof(g_httpAudioCtx));

    // 保存URL到上下文
    const char *url = (const char*)event->param1;
    if (url != NULL) {
        strncpy(g_httpAudioCtx.url, url, sizeof(g_httpAudioCtx.url) - 1);
        g_httpAudioCtx.url[sizeof(g_httpAudioCtx.url) - 1] = '\0';

        SK_LOGI(TAG, "Starting HTTP download for: %s", g_httpAudioCtx.url);

        // 创建HTTP下载任务
        BaseType_t ret = xTaskCreate(HttpDownloadTask, "HttpDL",
                                    4096,  // 4KB栈，专门负责HTTP下载
                                    &g_httpAudioCtx, 5, &g_httpAudioCtx.currentTask);

        if (ret != pdPASS) {
            SK_LOGE(TAG, "Failed to create HTTP download task");
            return SK_RET_FAIL;
        }
    } else {
        SK_LOGE(TAG, "Invalid URL parameter");
        return SK_RET_INVALID_PARAM;
    }

    return SK_RET_SUCCESS;
}

// HTTP音频状态停止处理
int32_t SmHttpAudioStop(SkSubStateInfo *info) {
    SK_LOGI(TAG, "HTTP audio state stopped");

    // 停止当前任务
    if (g_httpAudioCtx.currentTask != NULL) {
        vTaskDelete(g_httpAudioCtx.currentTask);
        g_httpAudioCtx.currentTask = NULL;
    }

    // 清理资源
    if (g_httpAudioCtx.downloadData) {
        SkHttpDownloadFree(g_httpAudioCtx.downloadData);
        free(g_httpAudioCtx.downloadData);
        g_httpAudioCtx.downloadData = NULL;
    }

    // 重置解析器
    SkOggResetParser();

    // 清理上下文
    memset(&g_httpAudioCtx, 0, sizeof(g_httpAudioCtx));

    return SK_RET_SUCCESS;
}

// HTTP音频事件处理
int32_t SmHttpAudioEventProc(SkSubStateInfo *info, const SkSmEvent *event) {
    if (event->event != SM_EVENT_HTTP_AUDIO) {
        return SK_RET_SUCCESS;
    }

    switch (event->subEvent) {
        case SM_EVENT_HTTP_DOWNLOAD_COMPLETE:
            SK_LOGI(TAG, "HTTP download completed, starting OGG parsing");
            // 创建OGG解析任务
            if (xTaskCreate(OggParseTask, "OggParse",
                           3072,  // 3KB栈，专门负责OGG解析
                           &g_httpAudioCtx, 5, &g_httpAudioCtx.currentTask) != pdPASS) {
                SK_LOGE(TAG, "Failed to create OGG parse task");
                g_httpAudioCtx.lastError = SK_RET_FAIL;
                SkSmSendEvent(&g_topStateCtrl, SM_EVENT_HTTP_AUDIO, SM_EVENT_HTTP_AUDIO_ERROR, 0, 0);
            }
            break;

        case SM_EVENT_OGG_PARSE_COMPLETE:
            SK_LOGI(TAG, "OGG parsing completed, starting OPUS queue");
            // 创建OPUS队列任务
            if (xTaskCreate(OpusQueueTask, "OpusQueue",
                           4096,  // 4KB栈，负责OPUS包队列和解码
                           &g_httpAudioCtx, 5, &g_httpAudioCtx.currentTask) != pdPASS) {
                SK_LOGE(TAG, "Failed to create OPUS queue task");
                g_httpAudioCtx.lastError = SK_RET_FAIL;
                SkSmSendEvent(&g_topStateCtrl, SM_EVENT_HTTP_AUDIO, SM_EVENT_HTTP_AUDIO_ERROR, 0, 0);
            }
            break;

        case SM_EVENT_OPUS_QUEUE_COMPLETE:
            SK_LOGI(TAG, "HTTP audio processing completed successfully");
            g_httpAudioCtx.currentState = HTTP_AUDIO_STATE_COMPLETE;
            // 清理资源并返回IDLE状态
            SmHttpAudioStop(info);
            SkSmEnterNewState(&g_topStateCtrl, STATE_IDLE, 0, 0, 0);
            break;

        case SM_EVENT_HTTP_AUDIO_ERROR:
            SK_LOGE(TAG, "HTTP audio processing error: %d", g_httpAudioCtx.lastError);
            g_httpAudioCtx.currentState = HTTP_AUDIO_STATE_ERROR;
            // 清理资源并返回IDLE状态
            SmHttpAudioStop(info);
            SkSmEnterNewState(&g_topStateCtrl, STATE_IDLE, 0, 0, 0);
            break;

        default:
            SK_LOGW(TAG, "Unknown HTTP audio event: %d", event->subEvent);
            break;
    }

    return SK_RET_SUCCESS;
}

// HTTP音频状态初始化
int32_t SkSmHttpAudioInit(SkSmItem *item, SkSmStateEndCallback endCb, SkStateHandler handler) {
    item->info.subState = 0;
    item->info.privateData = &g_httpAudioCtx;
    item->startProc = SmHttpAudioStart;
    item->stopProc = SmHttpAudioStop;
    item->eventProc = SmHttpAudioEventProc;

    SK_LOGI(TAG, "HTTP audio state machine initialized");
    return SK_RET_SUCCESS;
}

void SkSmSetReboot() {
    g_topStateCtrl.state = STATE_REBOOT;
    SkWifiStop();
    SkClinkSetFunFlag(CLINK_RUN_FLAG_STOP);
    SkRlinkSetFunFlag(RLINK_TASK_STOP);
    SkRlinkEventNotify(RLINK_EVENT_STOP_CALL, 0);
    SkSrProcessEnable(false);
    SkVcProcessEnable(false);
    SkRecorderStop();
    g_topStateCtrl.rebootCnt = 0;
    return;
}

void SkSmPlayLocalNotice(uint8_t *audioList, uint8_t len, bool waitFlag) {
    uint32_t workId;

    workId = SkOpusDecPlayLocal(audioList, len);
    if (waitFlag) {
        g_topStateCtrl.waitWorkId = workId;
        SK_LOGD(TAG, "Start audio play work, id=%d", g_topStateCtrl.waitWorkId);
    }
    return;
}

bool SkSmIsPlaying(void) {
    return (g_topStateCtrl.waitWorkId != 0);
}