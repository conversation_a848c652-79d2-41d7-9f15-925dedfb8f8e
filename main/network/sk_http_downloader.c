/**
 * @copyright: © 2025, 深圳市泛途智能科技有限公司. All rights reserved.
 * @file: sk_http_downloader.c
 * @description: HTTP文件下载到PSRAM模块实现
 * @author: <PERSON>
 * @date: 2025-01-20
 */
#include <string.h>
#include <stdlib.h>
#include "esp_log.h"
#include "esp_http_client.h"
#include "esp_heap_caps.h"
#include "freertos/FreeRTOS.h"
#include "freertos/ringbuf.h"
#include "sk_http_downloader.h"
#include "sk_opus_dec.h"
#include "sk_board.h"
#include "sk_log.h"
#include "sk_sm.h"

// 外部函数声明
extern sk_err_t SkOggResetParser(void);

static const char *TAG = "SkHttpDownloader";

#define HTTP_BUFFER_SIZE        4096
#define HTTP_TIMEOUT_MS         30000
#define RINGBUF_SIZE            (2 * 1024 * 1024)

// HTTP音频处理上下文
typedef struct {
    char url[256];                       // 音频URL
    SkHttpDownloadData *downloadData;    // 下载数据
    TaskHandle_t currentTask;            // 当前处理任务句柄
    SkStateHandler smHandler;            // 状态机句柄
} SkHttpAudioContext;

// 全局HTTP音频处理上下文
static SkHttpAudioContext g_httpAudioCtx = {0};

static uint8_t *g_audioData = NULL;
static size_t g_audioSize = 0;
static size_t g_currentPos = 0;
static bool g_initialized = false;

// OGG页面解析状态
static int g_currentSegment = 0;        // 当前段索引
static uint8_t g_currentSegments = 0;   // 当前页面的段数
static size_t g_currentPageDataStart = 0; // 当前页面数据起始位置
static bool g_parserFirstCall = true;   // 解析器首次调用标志

sk_err_t SkHttpDownloadFile(const char *url, SkHttpDownloadData *downloadData) {
    if (url == NULL || downloadData == NULL) {
        SK_LOGE(TAG, "Invalid parameters");
        return SK_RET_INVALID_PARAM;
    }

    memset(downloadData, 0, sizeof(SkHttpDownloadData));
    
    SK_LOGI(TAG, "Starting download: %s", url);
    
    // 配置HTTP客户端
    esp_http_client_config_t config = {
        .url = url,
        .timeout_ms = HTTP_TIMEOUT_MS,
    };
    
    esp_http_client_handle_t client = esp_http_client_init(&config);
    if (client == NULL) {
        SK_LOGE(TAG, "Failed to init HTTP client");
        return SK_RET_FAIL;
    }
    
    // 开始请求
    esp_err_t err = esp_http_client_open(client, 0);
    if (err != ESP_OK) {
        SK_LOGE(TAG, "Failed to open HTTP connection: %s", esp_err_to_name(err));
        esp_http_client_cleanup(client);
        return SK_RET_FAIL;
    }
    
    // 获取文件大小
    int content_length = esp_http_client_fetch_headers(client);
    if (content_length <= 0 || content_length > RINGBUF_SIZE) {
        SK_LOGE(TAG, "Invalid file size: %d", content_length);
        esp_http_client_close(client);
        esp_http_client_cleanup(client);
        return SK_RET_FAIL;
    }
    
    SK_LOGI(TAG, "File size: %d bytes", content_length);
    
    // 在PSRAM中分配环形缓冲区存储空间（包含数据空间和结构体空间）
    size_t totalSize = RINGBUF_SIZE + sizeof(StaticRingbuffer_t);
    downloadData->storage = heap_caps_malloc(totalSize, MALLOC_CAP_SPIRAM);
    if (downloadData->storage == NULL) {
        SK_LOGE(TAG, "Failed to allocate PSRAM: %zu bytes", totalSize);
        esp_http_client_close(client);
        esp_http_client_cleanup(client);
        return SK_RET_NO_MEMORY;
    }

    // 结构体放在存储空间的末尾
    StaticRingbuffer_t *structure = (StaticRingbuffer_t*)(downloadData->storage + RINGBUF_SIZE);

    // 创建静态环形缓冲区
    downloadData->ringbuf = xRingbufferCreateStatic(RINGBUF_SIZE, RINGBUF_TYPE_BYTEBUF,
                                                    downloadData->storage, structure);
    if (downloadData->ringbuf == NULL) {
        SK_LOGE(TAG, "Failed to create static ringbuffer");
        heap_caps_free(downloadData->storage);
        esp_http_client_close(client);
        esp_http_client_cleanup(client);
        return SK_RET_FAIL;
    }
    
    SK_LOGI(TAG, "Created ringbuffer in PSRAM: %d bytes", RINGBUF_SIZE);
    
    // 分配HTTP读取缓冲区
    uint8_t *buffer = malloc(HTTP_BUFFER_SIZE);
    if (buffer == NULL) {
        SK_LOGE(TAG, "Failed to allocate HTTP buffer");
        vRingbufferDelete(downloadData->ringbuf);
        heap_caps_free(downloadData->storage);
        esp_http_client_close(client);
        esp_http_client_cleanup(client);
        return SK_RET_NO_MEMORY;
    }
    
    // 下载数据到环形缓冲区
    size_t total_read = 0;
    while (total_read < content_length) {
        int data_read = esp_http_client_read(client, (char*)buffer, HTTP_BUFFER_SIZE);
        if (data_read < 0) {
            SK_LOGE(TAG, "HTTP read error: %d", data_read);
            free(buffer);
            vRingbufferDelete(downloadData->ringbuf);
            heap_caps_free(downloadData->storage);
            esp_http_client_close(client);
            esp_http_client_cleanup(client);
            return SK_RET_FAIL;
        }
        
        if (data_read == 0) {
            break;
        }
        
        if (total_read + data_read > content_length) {
            data_read = content_length - total_read;
        }
        
        // 写入环形缓冲区
        if (xRingbufferSend(downloadData->ringbuf, buffer, data_read, portMAX_DELAY) != pdTRUE) {
            SK_LOGE(TAG, "Failed to write to ringbuffer");
            free(buffer);
            vRingbufferDelete(downloadData->ringbuf);
            heap_caps_free(downloadData->storage);
            esp_http_client_close(client);
            esp_http_client_cleanup(client);
            return SK_RET_FAIL;
        }
        
        total_read += data_read;
        
        // 显示进度
        if (total_read % (content_length / 10) == 0 || total_read == content_length) {
            SK_LOGI(TAG, "Download progress: %zu/%d bytes (%d%%)", 
                   total_read, content_length, (int)(total_read * 100 / content_length));
        }
    }
    
    free(buffer);
    esp_http_client_close(client);
    esp_http_client_cleanup(client);
    
    if (total_read != content_length) {
        SK_LOGE(TAG, "Download incomplete: %zu/%d bytes", total_read, content_length);
        vRingbufferDelete(downloadData->ringbuf);
        heap_caps_free(downloadData->storage);
        return SK_RET_FAIL;
    }

    SK_LOGI(TAG, "Download completed: %zu bytes", total_read);
    downloadData->totalSize = total_read;
    
    return SK_RET_SUCCESS;
}

void SkHttpDownloadFree(SkHttpDownloadData *downloadData) {
    if (downloadData == NULL) {
        return;
    }

    if (downloadData->ringbuf != NULL) {
        vRingbufferDelete(downloadData->ringbuf);
        downloadData->ringbuf = NULL;
    }

    if (downloadData->storage != NULL) {
        heap_caps_free(downloadData->storage);
        downloadData->storage = NULL;
    }

    downloadData->totalSize = 0;
    SK_LOGI(TAG, "Download data freed from PSRAM");
}


/**
 * @brief 初始化OGG解析器，从环形缓冲区获取所有数据
 */
static sk_err_t InitOggParser(RingbufHandle_t ringbuf) {
    if (g_initialized) {
        return SK_RET_SUCCESS;
    }

    // 从环形缓冲区获取所有数据
    size_t itemSize;
    uint8_t *item = xRingbufferReceive(ringbuf, &itemSize, pdMS_TO_TICKS(1000));
    if (item == NULL) {
        return SK_RET_TIMEOUT;
    }

    // 分配内存并复制数据
    g_audioData = malloc(itemSize);
    if (g_audioData == NULL) {
        vRingbufferReturnItem(ringbuf, item);
        return SK_RET_NO_MEMORY;
    }

    memcpy(g_audioData, item, itemSize);
    g_audioSize = itemSize;
    g_currentPos = 0;
    g_initialized = true;

    vRingbufferReturnItem(ringbuf, item);

    SK_LOGI(TAG, "OGG parser initialized with %zu bytes", g_audioSize);
    return SK_RET_SUCCESS;
}

/**
 * @brief 解析OPUS头部信息
 */
sk_err_t SkOggParseOpusHeader(RingbufHandle_t ringbuf, SkOpusHeader *header) {
    if (ringbuf == NULL || header == NULL) {
        return SK_RET_INVALID_PARAM;
    }

    // 初始化解析器
    sk_err_t ret = InitOggParser(ringbuf);
    if (ret != SK_RET_SUCCESS) {
        return ret;
    }

    // 验证OGG格式
    if (g_audioSize < 46 || memcmp(g_audioData, "OggS", 4) != 0) {
        return SK_RET_FAIL;
    }

    // 找到OPUS头部数据（跳过OGG页面头部和段表）
    uint8_t segments = g_audioData[26];
    uint8_t *pageData = g_audioData + 27 + segments;

    // 验证OPUS头部
    if (memcmp(pageData, "OpusHead", 8) != 0) {
        return SK_RET_FAIL;
    }

    // 解析OPUS参数
    header->version = pageData[8];
    header->channels = pageData[9];
    header->preSkip = pageData[10] | (pageData[11] << 8);
    header->sampleRate = pageData[12] | (pageData[13] << 8) | (pageData[14] << 16) | (pageData[15] << 24);
    header->outputGain = pageData[16] | (pageData[17] << 8);

    SK_LOGI(TAG, "OPUS: %dch, %luHz, preSkip:%d",
           header->channels, header->sampleRate, header->preSkip);

    return SK_RET_SUCCESS;
}

/**
 * @brief 从缓存数据获取下一个OPUS数据包
 */
sk_err_t SkOggGetNextOpusPacket(RingbufHandle_t ringbuf, SkOpusPacket *packet) {
    if (ringbuf == NULL || packet == NULL) {
        return SK_RET_INVALID_PARAM;
    }

    // 确保解析器已初始化
    if (!g_initialized) {
        sk_err_t ret = InitOggParser(ringbuf);
        if (ret != SK_RET_SUCCESS) {
            return ret;
        }
    }

    // 第一次调用时，跳过头部和注释页面
    if (g_parserFirstCall) {
        g_currentPos = 0;
        g_currentSegment = 0;
        g_currentSegments = 0;

        // 跳过第一个页面（OPUS头部）
        if (memcmp(g_audioData, "OggS", 4) == 0) {
            uint8_t segments1 = g_audioData[26];
            size_t page1Size = 27 + segments1;
            for (int i = 0; i < segments1; i++) {
                page1Size += g_audioData[27 + i];
            }
            g_currentPos = page1Size;
        }

        // 跳过第二个页面（注释）
        if (g_currentPos + 27 < g_audioSize && memcmp(g_audioData + g_currentPos, "OggS", 4) == 0) {
            uint8_t segments2 = g_audioData[g_currentPos + 26];
            size_t page2Size = 27 + segments2;
            for (int i = 0; i < segments2; i++) {
                page2Size += g_audioData[g_currentPos + 27 + i];
            }
            g_currentPos += page2Size;
        }

        g_parserFirstCall = false;
        SK_LOGI(TAG, "Audio data starts at: %zu", g_currentPos);
    }

    // 如果当前页面的所有段都已处理完，移动到下一页面
    if (g_currentSegment >= g_currentSegments) {
        // 检查是否还有数据
        if (g_currentPos + 27 >= g_audioSize) {
            return SK_RET_NOT_FOUND;
        }

        // 验证当前页面
        if (memcmp(g_audioData + g_currentPos, "OggS", 4) != 0) {
            return SK_RET_FAIL;
        }

        // 解析新页面
        g_currentSegments = g_audioData[g_currentPos + 26];
        size_t headerSize = 27 + g_currentSegments;

        // 检查页面完整性
        size_t totalPageDataSize = 0;
        for (int i = 0; i < g_currentSegments; i++) {
            totalPageDataSize += g_audioData[g_currentPos + 27 + i];
        }

        if (g_currentPos + headerSize + totalPageDataSize > g_audioSize) {
            return SK_RET_FAIL;
        }

        g_currentPageDataStart = g_currentPos + headerSize;
        g_currentSegment = 0;

        // 减少页面切换日志输出
        static int pageLogCount = 0;
        pageLogCount++;
        if (pageLogCount <= 3 || pageLogCount % 20 == 0) {
            SK_LOGI(TAG, "New OGG page: %d segments, data start at %zu",
                   g_currentSegments, g_currentPageDataStart);
        }
    }

    // 提取当前段的OPUS包
    if (g_currentSegment >= g_currentSegments) {
        return SK_RET_NOT_FOUND;
    }

    // 计算当前包的大小和位置
    size_t packetSize = g_audioData[g_currentPos + 27 + g_currentSegment];
    size_t packetOffset = g_currentPageDataStart;

    // 计算当前包在页面数据中的偏移
    for (int i = 0; i < g_currentSegment; i++) {
        packetOffset += g_audioData[g_currentPos + 27 + i];
    }

    // 分配并复制单个OPUS包数据
    packet->data = malloc(packetSize);
    if (packet->data == NULL) {
        return SK_RET_NO_MEMORY;
    }

    memcpy(packet->data, g_audioData + packetOffset, packetSize);
    packet->size = packetSize;

    // 移动到下一个段
    g_currentSegment++;

    // 如果当前页面的所有段都处理完了，移动到下一页面
    if (g_currentSegment >= g_currentSegments) {
        size_t headerSize = 27 + g_currentSegments;
        size_t totalPageDataSize = 0;
        for (int i = 0; i < g_currentSegments; i++) {
            totalPageDataSize += g_audioData[g_currentPos + 27 + i];
        }
        g_currentPos += headerSize + totalPageDataSize;
    }

    // 减少日志输出，只在关键时刻输出
    static int packetLogCount = 0;
    packetLogCount++;
    if (packetLogCount <= 3 || packetLogCount % 100 == 0) {
        SK_LOGI(TAG, "OPUS packet: %zu bytes (segment %d/%d)",
               packet->size, g_currentSegment, g_currentSegments);
    }
    return SK_RET_SUCCESS;
}

/**
 * @brief 重置OGG解析位置（不释放数据，用于重新开始解析）
 */
void SkOggResetParserPosition(void) {
    g_currentPos = 0;
    g_currentSegment = 0;
    g_currentSegments = 0;
    g_currentPageDataStart = 0;
    g_parserFirstCall = true;
    SK_LOGI(TAG, "OGG parser position reset");
}

/**
 * @brief 重置OGG解析状态（释放数据，完全重置）
 */
void SkOggResetParser(void) {
    if (g_audioData) {
        free(g_audioData);
        g_audioData = NULL;
    }
    g_audioSize = 0;
    g_currentPos = 0;
    g_currentSegment = 0;
    g_currentSegments = 0;
    g_currentPageDataStart = 0;
    g_parserFirstCall = true;  // 重置首次调用标志
    g_initialized = false;
    SK_LOGI(TAG, "OGG parser reset");
}

/**
 * @brief 释放OPUS包资源
 */
void SkOggFreeOpusPacket(SkOpusPacket *packet) {
    if (packet && packet->data) {
        free(packet->data);
        packet->data = NULL;
        packet->size = 0;
    }
}

/**
 * @brief 重置OGG解析状态（用于队列流式处理）
 */
static void ResetOggParserState(void) {
    g_currentPos = 0;
    g_currentSegment = 0;
    g_currentSegments = 0;
    g_currentPageDataStart = 0;
    g_parserFirstCall = true;
    SK_LOGI(TAG, "OGG parser state reset for queue streaming");
}

/**
 * @brief 队列集成OPUS流式播放（利用现有音频队列系统）
 */
sk_err_t SkOpusQueueStreamPlay(SkHttpDownloadData *downloadData) {
    if (downloadData == NULL) {
        return SK_RET_INVALID_PARAM;
    }

    SK_LOGI(TAG, "=== Starting Queue-Based Stream Play ===");

    // 1. 获取现有的OPUS解码器
    SkOpusDecHandler handler = SkOpusDecGetHandler();
    if (handler == NULL) {
        SK_LOGE(TAG, "OPUS decoder not available");
        return SK_RET_FAIL;
    }

    // 2. 确保解析器已初始化
    if (!g_initialized) {
        sk_err_t ret = InitOggParser(downloadData->ringbuf);
        if (ret != SK_RET_SUCCESS) {
            return ret;
        }
    }

    // 3. 重置解析状态
    ResetOggParserState();

    int processedCount = 0;
    int playedCount = 0;

    while (true) {
        SkOpusPacket packet;

        // 从PSRAM取一个OPUS包
        sk_err_t ret = SkOggGetNextOpusPacket(downloadData->ringbuf, &packet);
        if (ret != SK_RET_SUCCESS) {
            if (ret == SK_RET_NOT_FOUND) {
                SK_LOGI(TAG, "All packets processed");
            } else {
                SK_LOGE(TAG, "Failed to get packet %d: error %d", processedCount + 1, ret);
            }
            break;
        }

        processedCount++;

        // 使用静态PCM缓冲区（减少malloc/free开销）
        static int16_t pcmBuffer[960];

        // 使用现有解码器解码OPUS包
        int32_t samples = SkOpusDecDecode(handler, packet.data, packet.size, pcmBuffer, 960);

        if (samples > 0) {
            // 启动扬声器（如果还没启动）
            static bool spkStarted = false;
            if (!spkStarted) {
                SkBspStartSpk();
                spkStarted = true;
                SK_LOGI(TAG, "Speaker started");
            }

            // 使用现有音频播放接口播放PCM数据
            size_t pcmBytes = samples * sizeof(int16_t);
            sk_err_t playRet = SkBspPlayAudio(pcmBuffer, pcmBytes, 100); // 增加超时时间

            if (playRet == SK_RET_SUCCESS) {
                playedCount++;

                // 只在前几个包显示详细信息
                if (playedCount <= 3) {
                    SK_LOGI(TAG, "🎵 Played #%d: %zu bytes → %ld samples",
                           playedCount, packet.size, samples);
                } else if (playedCount % 100 == 0) {
                    SK_LOGI(TAG, "🎵 Progress: %d processed, %d played", processedCount, playedCount);
                }
            } else {
                SK_LOGE(TAG, "Failed to play audio: %d", playRet);
            }
        }
        SkOggFreeOpusPacket(&packet);
    }

    SK_LOGI(TAG, "=== Queue Stream Play Complete ===");
    SK_LOGI(TAG, "Total: %d processed, %d played (%.1f%% success)",
           processedCount, playedCount,
           processedCount > 0 ? (float)playedCount * 100.0f / processedCount : 0.0f);

    if (playedCount > 0) {
        SK_LOGI(TAG, "Audio packets played successfully via existing system");
        float duration = (float)playedCount * 60.0f / 1000.0f; // 每包60ms
        SK_LOGI(TAG, "Estimated playback duration: %.2f seconds", duration);
    } else {
        SK_LOGE(TAG, "No packets played successfully");
    }

    return (playedCount > 0) ? SK_RET_SUCCESS : SK_RET_FAIL;
}

// ==================== 状态机HTTP音频处理任务 ====================

// HTTP下载任务
static void HttpDownloadTask(void *param) {
    SkHttpAudioContext *ctx = (SkHttpAudioContext*)param;

    SK_LOGI(TAG, "HTTP download task started for: %s", ctx->url);

    // 分配下载数据结构
    ctx->downloadData = malloc(sizeof(SkHttpDownloadData));
    if (ctx->downloadData == NULL) {
        SK_LOGE(TAG, "Failed to allocate download data");

        SkSmSendEvent(ctx->smHandler, SM_EVENT_HTTP_AUDIO, SM_EVENT_HTTP_AUDIO_ERROR, 0, 0);
        ctx->currentTask = NULL;
        vTaskDelete(NULL);
        return;
    }

    // 执行HTTP下载
    sk_err_t ret = SkHttpDownloadFile(ctx->url, ctx->downloadData);

    if (ret == SK_RET_SUCCESS) {
        SK_LOGI(TAG, "HTTP download completed: %zu bytes", ctx->downloadData->totalSize);
        // 通知状态机：下载完成，进入OGG解析状态
        SkSmSendEvent(ctx->smHandler, SM_EVENT_HTTP_AUDIO, SM_EVENT_HTTP_DOWNLOAD_COMPLETE, 0, 0);
    } else {
        SK_LOGE(TAG, "HTTP download failed: %d", ret);

        SkSmSendEvent(ctx->smHandler, SM_EVENT_HTTP_AUDIO, SM_EVENT_HTTP_AUDIO_ERROR, 0, 0);
    }

    ctx->currentTask = NULL;
    vTaskDelete(NULL);
}

// OGG解析任务
static void OggParseTask(void *param) {
    SkHttpAudioContext *ctx = (SkHttpAudioContext*)param;

    SK_LOGI(TAG, "OGG parse task started");

    if (ctx->downloadData == NULL) {
        SK_LOGE(TAG, "Invalid download data");

        SkSmSendEvent(ctx->smHandler, SM_EVENT_HTTP_AUDIO, SM_EVENT_HTTP_AUDIO_ERROR, 0, 0);
        ctx->currentTask = NULL;
        vTaskDelete(NULL);
        return;
    }

    // 解析OPUS头部信息
    SkOpusHeader header;
    sk_err_t ret = SkOggParseOpusHeader(ctx->downloadData->ringbuf, &header);

    if (ret == SK_RET_SUCCESS) {
        SK_LOGI(TAG, "OGG parsing completed: %dch, %luHz",
               header.channels, header.sampleRate);
        // 通知状态机：OGG解析完成，进入OPUS队列状态
        SkSmSendEvent(ctx->smHandler, SM_EVENT_HTTP_AUDIO, SM_EVENT_OGG_PARSE_COMPLETE, 0, 0);
    } else {
        SK_LOGE(TAG, "OGG parsing failed: %d", ret);

        SkSmSendEvent(ctx->smHandler, SM_EVENT_HTTP_AUDIO, SM_EVENT_HTTP_AUDIO_ERROR, 0, 0);
    }

    ctx->currentTask = NULL;
    vTaskDelete(NULL);
}

// OPUS队列播放任务
static void OpusQueueTask(void *param) {
    SkHttpAudioContext *ctx = (SkHttpAudioContext*)param;

    SK_LOGI(TAG, "OPUS queue task started");

    if (ctx->downloadData == NULL) {
        SK_LOGE(TAG, "Invalid download data");

        SkSmSendEvent(ctx->smHandler, SM_EVENT_HTTP_AUDIO, SM_EVENT_HTTP_AUDIO_ERROR, 0, 0);
        ctx->currentTask = NULL;
        vTaskDelete(NULL);
        return;
    }

    // 获取OPUS解码器句柄
    SkOpusDecHandler handler = SkOpusDecGetHandler();
    if (handler == NULL) {
        SK_LOGE(TAG, "OPUS decoder not available");

        SkSmSendEvent(ctx->smHandler, SM_EVENT_HTTP_AUDIO, SM_EVENT_HTTP_AUDIO_ERROR, 0, 0);
        ctx->currentTask = NULL;
        vTaskDelete(NULL);
        return;
    }

    // 重置OGG解析状态
    SkOggResetParserPosition();

    uint32_t processedPackets = 0;
    bool spkStarted = false;

    // OPUS包处理循环
    while (true) {
        SkOpusPacket packet;

        // 获取下一个OPUS包
        sk_err_t ret = SkOggGetNextOpusPacket(ctx->downloadData->ringbuf, &packet);
        if (ret != SK_RET_SUCCESS) {
            if (ret == SK_RET_NOT_FOUND) {
                SK_LOGI(TAG, "All OPUS packets processed: %lu", processedPackets);
                break;
            } else {
                SK_LOGE(TAG, "Failed to get OPUS packet: %d", ret);
                SkSmSendEvent(ctx->smHandler, SM_EVENT_HTTP_AUDIO, SM_EVENT_HTTP_AUDIO_ERROR, 0, 0);
                ctx->currentTask = NULL;
                vTaskDelete(NULL);
                return;
            }
        }

        processedPackets++;

        // 使用静态PCM缓冲区（在任务栈中，不在主任务栈）
        static int16_t pcmBuffer[960];

        // 解码OPUS包
        int32_t samples = SkOpusDecDecode(handler, packet.data, packet.size, pcmBuffer, 960);

        if (samples > 0) {
            // 启动扬声器（如果还没启动）
            if (!spkStarted) {
                SkBspStartSpk();
                spkStarted = true;
                SK_LOGI(TAG, "Speaker started for audio playback");
            }

            // 播放PCM数据
            size_t pcmBytes = samples * sizeof(int16_t);
            sk_err_t playRet = SkBspPlayAudio(pcmBuffer, pcmBytes, 100);

            if (playRet != SK_RET_SUCCESS) {
                SK_LOGE(TAG, "Failed to play audio: %d", playRet);
            }
        }

        // 释放OPUS包
        SkOggFreeOpusPacket(&packet);

        // 定期让出CPU时间
        if (processedPackets % 10 == 0) {
            vTaskDelay(pdMS_TO_TICKS(1));
        }
    }

    SK_LOGI(TAG, "OPUS queue processing completed: %lu packets", processedPackets);

    // 通知状态机：OPUS队列处理完成
    SkSmSendEvent(ctx->smHandler, SM_EVENT_HTTP_AUDIO, SM_EVENT_OPUS_QUEUE_COMPLETE, 0, 0);

    ctx->currentTask = NULL;
    vTaskDelete(NULL);
}

// ==================== 状态机处理函数 ====================

// HTTP音频状态开始处理
static int32_t SmHttpAudioStart(SkSubStateInfo *info, const SkSmEvent *event) {
    SK_LOGI(TAG, "HTTP audio state started");

    // 清理之前的上下文
    memset(&g_httpAudioCtx, 0, sizeof(g_httpAudioCtx));

    // 保存状态机句柄
    g_httpAudioCtx.smHandler = (SkStateHandler)info->privateData;

    // 保存URL到上下文
    const char *url = (const char*)event->param1;
    if (url != NULL) {
        strncpy(g_httpAudioCtx.url, url, sizeof(g_httpAudioCtx.url) - 1);
        g_httpAudioCtx.url[sizeof(g_httpAudioCtx.url) - 1] = '\0';

        SK_LOGI(TAG, "Starting HTTP download for: %s", g_httpAudioCtx.url);

        // 创建HTTP下载任务
        BaseType_t ret = xTaskCreate(HttpDownloadTask, "HttpDL",
                                    4096,  // 4KB栈，专门负责HTTP下载
                                    &g_httpAudioCtx, 5, &g_httpAudioCtx.currentTask);

        if (ret != pdPASS) {
            SK_LOGE(TAG, "Failed to create HTTP download task");
            return SK_RET_FAIL;
        }
    } else {
        SK_LOGE(TAG, "Invalid URL parameter");
        return SK_RET_INVALID_PARAM;
    }

    return SK_RET_SUCCESS;
}

// HTTP音频状态停止处理
static int32_t SmHttpAudioStop(SkSubStateInfo *info) {
    SK_LOGI(TAG, "HTTP audio state stopped");

    // 停止当前任务
    if (g_httpAudioCtx.currentTask != NULL) {
        vTaskDelete(g_httpAudioCtx.currentTask);
        g_httpAudioCtx.currentTask = NULL;
    }

    // 清理资源
    if (g_httpAudioCtx.downloadData) {
        SkHttpDownloadFree(g_httpAudioCtx.downloadData);
        free(g_httpAudioCtx.downloadData);
        g_httpAudioCtx.downloadData = NULL;
    }

    // 重置解析器
    SkOggResetParser();

    // 清理上下文
    memset(&g_httpAudioCtx, 0, sizeof(g_httpAudioCtx));

    return SK_RET_SUCCESS;
}



// HTTP音频状态初始化
int32_t SkSmHttpAudioInit(SkSmItem *item, SkSmStateEndCallback endCb, SkStateHandler handler) {
    item->info.subState = 0;
    item->info.privateData = handler;  // 保存状态机句柄
    item->startProc = SmHttpAudioStart;
    item->stopProc = SmHttpAudioStop;
    item->eventProc = NULL;  // 事件处理在 sm_top.c 中

    SK_LOGI(TAG, "HTTP audio state machine initialized");
    return SK_RET_SUCCESS;
}

// HTTP音频任务控制接口
sk_err_t SkHttpAudioStartOggParsing(void) {
    if (g_httpAudioCtx.downloadData == NULL) {
        SK_LOGE(TAG, "No download data available for OGG parsing");
        return SK_RET_INVALID_PARAM;
    }

    // 创建OGG解析任务
    BaseType_t ret = xTaskCreate(OggParseTask, "OggParse",
                                3072,  // 3KB栈，专门负责OGG解析
                                &g_httpAudioCtx, 5, &g_httpAudioCtx.currentTask);

    if (ret != pdPASS) {
        SK_LOGE(TAG, "Failed to create OGG parse task");
        return SK_RET_FAIL;
    }

    return SK_RET_SUCCESS;
}

sk_err_t SkHttpAudioStartOpusQueue(void) {
    if (g_httpAudioCtx.downloadData == NULL) {
        SK_LOGE(TAG, "No download data available for OPUS queue");
        return SK_RET_INVALID_PARAM;
    }

    // 创建OPUS队列任务
    BaseType_t ret = xTaskCreate(OpusQueueTask, "OpusQueue",
                                4096,  // 4KB栈，负责OPUS包队列和解码
                                &g_httpAudioCtx, 5, &g_httpAudioCtx.currentTask);

    if (ret != pdPASS) {
        SK_LOGE(TAG, "Failed to create OPUS queue task");
        return SK_RET_FAIL;
    }

    return SK_RET_SUCCESS;
}
